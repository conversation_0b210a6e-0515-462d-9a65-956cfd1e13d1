#!/usr/bin/env node
/**
 * Performance Testing Suite for Real-Time Streaming Transcription
 * 
 * Tests the complete pipeline and validates performance targets:
 * - Latency < 500ms for interim results
 * - CPU usage reduction vs previous implementation
 * - Transcription accuracy validation
 * - Memory usage optimization
 */

const { performance } = require('perf_hooks');
const os = require('os');

// Performance metrics tracking
class PerformanceMetrics {
  constructor() {
    this.metrics = {
      latency: [],
      cpuUsage: [],
      memoryUsage: [],
      transcriptionAccuracy: [],
      streamingSuccess: 0,
      fallbackCount: 0,
      errorCount: 0
    };
    this.startTime = performance.now();
    this.initialMemory = process.memoryUsage();
  }

  recordLatency(latencyMs) {
    this.metrics.latency.push(latencyMs);
  }

  recordCpuUsage() {
    const cpuUsage = process.cpuUsage();
    this.metrics.cpuUsage.push(cpuUsage);
  }

  recordMemoryUsage() {
    const memUsage = process.memoryUsage();
    this.metrics.memoryUsage.push(memUsage);
  }

  recordStreamingSuccess() {
    this.metrics.streamingSuccess++;
  }

  recordFallback() {
    this.metrics.fallbackCount++;
  }

  recordError() {
    this.metrics.errorCount++;
  }

  getAverageLatency() {
    if (this.metrics.latency.length === 0) return 0;
    return this.metrics.latency.reduce((a, b) => a + b, 0) / this.metrics.latency.length;
  }

  getMaxLatency() {
    return Math.max(...this.metrics.latency, 0);
  }

  getMinLatency() {
    return Math.min(...this.metrics.latency, Infinity);
  }

  getMemoryDelta() {
    const current = process.memoryUsage();
    return {
      heapUsed: current.heapUsed - this.initialMemory.heapUsed,
      heapTotal: current.heapTotal - this.initialMemory.heapTotal,
      external: current.external - this.initialMemory.external,
      rss: current.rss - this.initialMemory.rss
    };
  }

  generateReport() {
    const totalTime = performance.now() - this.startTime;
    const avgLatency = this.getAverageLatency();
    const maxLatency = this.getMaxLatency();
    const minLatency = this.getMinLatency();
    const memoryDelta = this.getMemoryDelta();

    return {
      summary: {
        totalTestTime: Math.round(totalTime),
        streamingSuccessRate: this.metrics.streamingSuccess / (this.metrics.streamingSuccess + this.metrics.fallbackCount) * 100,
        errorRate: this.metrics.errorCount / (this.metrics.streamingSuccess + this.metrics.fallbackCount + this.metrics.errorCount) * 100
      },
      latency: {
        average: Math.round(avgLatency),
        maximum: Math.round(maxLatency),
        minimum: Math.round(minLatency),
        target: 500, // Target: < 500ms
        meetsTarget: avgLatency < 500
      },
      memory: {
        heapUsedDelta: Math.round(memoryDelta.heapUsed / 1024 / 1024 * 100) / 100, // MB
        heapTotalDelta: Math.round(memoryDelta.heapTotal / 1024 / 1024 * 100) / 100, // MB
        rssDelta: Math.round(memoryDelta.rss / 1024 / 1024 * 100) / 100 // MB
      },
      counts: {
        streamingSuccess: this.metrics.streamingSuccess,
        fallbacks: this.metrics.fallbackCount,
        errors: this.metrics.errorCount
      }
    };
  }
}

async function testStreamingPerformance() {
  console.log('🚀 Starting Real-Time Streaming Transcription Performance Tests\n');
  
  const metrics = new PerformanceMetrics();
  
  try {
    // Test 1: Service Availability and Initialization
    console.log('📋 Test 1: Service Availability and Initialization');
    const startInit = performance.now();
    
    const PythonStreamingBridge = require('./dist/electron/helpers/PythonStreamingBridge').default;
    const RealTimeVoiceService = require('./dist/electron/helpers/RealTimeVoiceService').default;
    
    const bridge = PythonStreamingBridge.getInstance();
    const voiceService = RealTimeVoiceService.getInstance();
    
    // Check streaming availability
    const isAvailable = await bridge.isStreamingAvailable();
    const initTime = performance.now() - startInit;
    
    console.log(`   ✅ Streaming available: ${isAvailable}`);
    console.log(`   ⏱️  Initialization time: ${Math.round(initTime)}ms`);
    
    if (isAvailable) {
      metrics.recordLatency(initTime);
      
      // Test 2: Bridge Initialization Performance
      console.log('\n📋 Test 2: Bridge Initialization Performance');
      const startBridgeInit = performance.now();
      
      const initialized = await bridge.initialize({
        modelSize: 'tiny.en',
        enableInterimResults: true
      });
      
      const bridgeInitTime = performance.now() - startBridgeInit;
      console.log(`   ✅ Bridge initialized: ${initialized}`);
      console.log(`   ⏱️  Bridge init time: ${Math.round(bridgeInitTime)}ms`);
      
      if (initialized) {
        metrics.recordLatency(bridgeInitTime);
        metrics.recordStreamingSuccess();
        
        // Test 3: Audio Processing Latency
        console.log('\n📋 Test 3: Audio Processing Latency');
        
        const sessionId = bridge.startSession();
        console.log(`   📝 Session started: ${sessionId}`);
        
        // Generate test audio chunks and measure processing latency
        for (let i = 0; i < 10; i++) {
          const startChunk = performance.now();
          
          // Generate test audio (100ms of silence)
          const testAudio = Buffer.alloc(1600, 0);
          await bridge.sendAudioChunk(testAudio);
          
          const chunkTime = performance.now() - startChunk;
          metrics.recordLatency(chunkTime);
          metrics.recordMemoryUsage();
          
          console.log(`   📤 Chunk ${i + 1}/10 processed in ${Math.round(chunkTime)}ms`);
          
          // Small delay between chunks
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        // Test 4: Session Finalization Performance
        console.log('\n📋 Test 4: Session Finalization Performance');
        const startFinalize = performance.now();
        
        await bridge.finalizeSession();
        
        const finalizeTime = performance.now() - startFinalize;
        console.log(`   ✅ Session finalized in ${Math.round(finalizeTime)}ms`);
        metrics.recordLatency(finalizeTime);
        
        // Test 5: Health Check Performance
        console.log('\n📋 Test 5: Health Check Performance');
        const startPing = performance.now();
        
        const pingResult = await bridge.ping();
        
        const pingTime = performance.now() - startPing;
        console.log(`   ✅ Health check: ${pingResult} (${Math.round(pingTime)}ms)`);
        metrics.recordLatency(pingTime);
        
        // Cleanup
        await bridge.shutdown();
        
      } else {
        metrics.recordFallback();
      }
    } else {
      metrics.recordFallback();
      console.log('   ℹ️  Testing fallback to batch mode...');
      
      // Test batch mode performance
      const voiceStatus = voiceService.getStatus();
      console.log(`   ✅ Voice service status: ${JSON.stringify(voiceStatus)}`);
    }
    
    // Test 6: Memory Usage Analysis
    console.log('\n📋 Test 6: Memory Usage Analysis');
    metrics.recordMemoryUsage();
    
    const memoryDelta = metrics.getMemoryDelta();
    console.log(`   📊 Heap used delta: ${memoryDelta.heapUsed / 1024 / 1024} MB`);
    console.log(`   📊 RSS delta: ${memoryDelta.rss / 1024 / 1024} MB`);
    
    // Test 7: System Resource Usage
    console.log('\n📋 Test 7: System Resource Usage');
    const cpuUsage = process.cpuUsage();
    const memUsage = process.memoryUsage();
    
    console.log(`   🖥️  CPU User: ${cpuUsage.user / 1000}ms`);
    console.log(`   🖥️  CPU System: ${cpuUsage.system / 1000}ms`);
    console.log(`   💾 Memory RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
    console.log(`   💾 Memory Heap: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
    
  } catch (error) {
    console.error('❌ Performance test error:', error);
    metrics.recordError();
  }
  
  // Generate and display performance report
  console.log('\n📊 Performance Test Results');
  console.log('=' .repeat(50));
  
  const report = metrics.generateReport();
  
  console.log(`\n🎯 Latency Performance:`);
  console.log(`   Average: ${report.latency.average}ms (Target: <${report.latency.target}ms)`);
  console.log(`   Range: ${report.latency.minimum}ms - ${report.latency.maximum}ms`);
  console.log(`   Meets Target: ${report.latency.meetsTarget ? '✅ YES' : '❌ NO'}`);
  
  console.log(`\n💾 Memory Performance:`);
  console.log(`   Heap Used Delta: ${report.memory.heapUsedDelta}MB`);
  console.log(`   RSS Delta: ${report.memory.rssDelta}MB`);
  
  console.log(`\n📈 Success Metrics:`);
  console.log(`   Streaming Success Rate: ${report.summary.streamingSuccessRate.toFixed(1)}%`);
  console.log(`   Error Rate: ${report.summary.errorRate.toFixed(1)}%`);
  console.log(`   Total Test Time: ${report.summary.totalTestTime}ms`);
  
  console.log(`\n📊 Operation Counts:`);
  console.log(`   Streaming Operations: ${report.counts.streamingSuccess}`);
  console.log(`   Fallback Operations: ${report.counts.fallbacks}`);
  console.log(`   Errors: ${report.counts.errors}`);
  
  // Performance assessment
  console.log(`\n🏆 Overall Assessment:`);
  
  const isPerformant = report.latency.meetsTarget && 
                      report.summary.errorRate < 5 && 
                      report.memory.heapUsedDelta < 100;
  
  if (isPerformant) {
    console.log('   ✅ EXCELLENT - All performance targets met!');
    console.log('   🚀 Real-time streaming transcription is ready for production');
  } else {
    console.log('   ⚠️  NEEDS OPTIMIZATION - Some targets not met');
    if (!report.latency.meetsTarget) {
      console.log('   📈 Latency optimization needed');
    }
    if (report.summary.errorRate >= 5) {
      console.log('   🔧 Error handling needs improvement');
    }
    if (report.memory.heapUsedDelta >= 100) {
      console.log('   💾 Memory usage optimization needed');
    }
  }
  
  console.log('\n🎉 Performance testing completed!');
  
  return report;
}

// Run performance tests
if (require.main === module) {
  testStreamingPerformance()
    .then((report) => {
      process.exit(report.latency.meetsTarget ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 Performance test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testStreamingPerformance, PerformanceMetrics };
